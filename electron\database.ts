import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NodeSSH } from 'node-ssh';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { logger, log } from '../src/utils/Logger';
import { config } from '../src/config/AppConfig';
import { roleManager, Permission } from '../src/security/RoleManager';
import { systemMonitor } from '../src/monitoring/SystemMonitor';
import { notificationManager } from '../src/notifications/NotificationManager';
import { incrementalBackupManager } from '../src/backup/IncrementalBackup';

// 数据库配置
const DB_CONFIG = {
  host: '***************',
  user: 'user-hiram',
  password: 'user-hiram',
  database: 'user',
  port: 3306
};

// JWT密钥
const JWT_SECRET = 'mysql-backup-app-secret-key-2024';

// 数据库连接池
let pool: mysql.Pool;

// 初始化数据库连接
export function initDatabase() {
  pool = mysql.createPool({
    ...DB_CONFIG,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
  });
}

// 创建用户表（如果不存在）
export async function createUserTable() {
  try {
    const connection = await pool.getConnection();
    
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;
    
    await connection.execute(createTableSQL);
    connection.release();
    
    console.log('User table created successfully or already exists');
  } catch (error) {
    console.error('创建用户表失败:', error);
    throw error;
  }
}

// 用户注册
export async function registerUser(username: string, email: string, password: string) {
  try {
    const connection = await pool.getConnection();
    
    // 检查用户名是否已存在
    const [existingUsers] = await connection.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    ) as [any[], any];
    
    if (existingUsers.length > 0) {
      connection.release();
      const existingUser = existingUsers[0];
      if (existingUser.username === username) {
        return { success: false, message: '用户名已存在' };
      } else {
        return { success: false, message: '邮箱已被注册' };
      }
    }
    
    // 密码加密
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // 插入新用户
    const [result] = await connection.execute(
      'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
      [username, email, passwordHash]
    ) as [any, any];
    
    connection.release();
    
    return { 
      success: true, 
      message: '注册成功',
      userId: result.insertId 
    };
    
  } catch (error) {
    console.error('用户注册失败:', error);
    return { success: false, message: '注册失败，请稍后重试' };
  }
}

// 用户登录
export async function loginUser(username: string, password: string) {
  try {
    const connection = await pool.getConnection();
    
    // 查找用户
    const [users] = await connection.execute(
      'SELECT id, username, email, password_hash FROM users WHERE username = ?',
      [username]
    ) as [any[], any];
    
    connection.release();
    
    if (users.length === 0) {
      return { success: false, message: '用户名或密码错误' };
    }
    
    const user = users[0];
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      return { success: false, message: '用户名或密码错误' };
    }
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        email: user.email 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    return {
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email
      }
    };
    
  } catch (error) {
    console.error('用户登录失败:', error);
    return { success: false, message: '登录失败，请稍后重试' };
  }
}

// 验证JWT token
export function verifyToken(token: string) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return { success: true, data: decoded };
  } catch (error) {
    return { success: false, message: 'Token无效或已过期' };
  }
}

// 邮箱格式验证
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 创建服务器表
export async function createServerTable() {
  try {
    const connection = await pool.getConnection();

    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS servers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        host VARCHAR(255) NOT NULL,
        port INT DEFAULT 3306,
        username VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        ssh_host VARCHAR(255),
        ssh_port INT DEFAULT 22,
        ssh_username VARCHAR(100),
        ssh_password VARCHAR(255),
        ssh_private_key TEXT,
        status ENUM('active', 'inactive', 'error') DEFAULT 'inactive',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `;

    await connection.execute(createTableQuery);
    connection.release();
    console.log('Server table created successfully or already exists');
  } catch (error) {
    console.error('创建服务器表失败:', error);
    throw error;
  }
}

// 创建备份任务表
export async function createBackupTaskTable() {
  try {
    const connection = await pool.getConnection();

    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS backup_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        server_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        database_name VARCHAR(100) NOT NULL,
        backup_type ENUM('full', 'incremental') DEFAULT 'incremental',
        schedule_type ENUM('manual', 'hourly', 'daily', 'weekly', 'monthly', 'custom') DEFAULT 'manual',
        schedule_time TIME,
        schedule_day INT,
        custom_interval_minutes INT DEFAULT NULL COMMENT '自定义备份间隔（分钟）',
        backup_path VARCHAR(500),
        retention_days INT DEFAULT 30,
        status ENUM('active', 'inactive', 'running', 'error') DEFAULT 'active',
        last_backup_time TIMESTAMP NULL,
        last_backup_size BIGINT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
      )
    `;

    await connection.execute(createTableQuery);

    // 检查并添加新字段（如果表已存在但缺少新字段）
    try {
      await connection.execute(`
        ALTER TABLE backup_tasks
        ADD COLUMN IF NOT EXISTS custom_interval_minutes INT DEFAULT NULL COMMENT '自定义备份间隔（分钟）'
      `);

      await connection.execute(`
        ALTER TABLE backup_tasks
        MODIFY COLUMN schedule_type ENUM('manual', 'hourly', 'daily', 'weekly', 'monthly', 'custom') DEFAULT 'manual'
      `);
    } catch (alterError) {
      // 忽略字段已存在的错误
      console.log('Table structure update completed or already up to date');
    }

    connection.release();
    console.log('Backup task table created successfully or already exists');
  } catch (error) {
    console.error('创建备份任务表失败:', error);
    throw error;
  }
}

// 创建备份历史表
export async function createBackupHistoryTable() {
  try {
    const connection = await pool.getConnection();

    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS backup_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        backup_type ENUM('full', 'incremental') NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP NULL,
        status ENUM('running', 'completed', 'failed') DEFAULT 'running',
        error_message TEXT,
        binlog_file VARCHAR(255),
        binlog_position BIGINT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES backup_tasks(id) ON DELETE CASCADE
      )
    `;

    await connection.execute(createTableQuery);
    connection.release();
    console.log('Backup history table created successfully or already exists');
  } catch (error) {
    console.error('创建备份历史表失败:', error);
    throw error;
  }
}

// 添加服务器
export async function addServer(serverData: {
  userId: number;
  name: string;
  host: string;
  port: number;
  username: string;
  password: string;
  sshHost?: string;
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string;
  sshPrivateKey?: string;
}) {
  try {
    const connection = await pool.getConnection();

    // 检查服务器名称是否已存在
    const [existing] = await connection.execute(
      'SELECT id FROM servers WHERE user_id = ? AND name = ?',
      [serverData.userId, serverData.name]
    ) as [any[], any];

    if (existing.length > 0) {
      connection.release();
      return { success: false, message: '服务器名称已存在' };
    }

    // 插入新服务器
    const [result] = await connection.execute(
      `INSERT INTO servers (user_id, name, host, port, username, password, ssh_host, ssh_port, ssh_username, ssh_password, ssh_private_key)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        serverData.userId,
        serverData.name,
        serverData.host,
        serverData.port,
        serverData.username,
        serverData.password,
        serverData.sshHost || null,
        serverData.sshPort || null,
        serverData.sshUsername || null,
        serverData.sshPassword || null,
        serverData.sshPrivateKey || null
      ]
    ) as [any, any];

    connection.release();

    return {
      success: true,
      message: '服务器添加成功',
      serverId: (result as any).insertId
    };

  } catch (error) {
    console.error('添加服务器失败:', error);
    return { success: false, message: '添加服务器失败，请稍后重试' };
  }
}

// 获取用户的服务器列表
export async function getUserServers(userId: number) {
  try {
    const connection = await pool.getConnection();

    const [servers] = await connection.execute(
      `SELECT id, name, host, port, username, ssh_host, ssh_port, ssh_username, status, created_at, updated_at
       FROM servers WHERE user_id = ? ORDER BY created_at DESC`,
      [userId]
    ) as [any[], any];

    connection.release();

    return {
      success: true,
      servers
    };

  } catch (error) {
    console.error('获取服务器列表失败:', error);
    return { success: false, message: '获取服务器列表失败' };
  }
}

// 获取单个服务器信息
export async function getServerById(serverId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    const [servers] = await connection.execute(
      'SELECT * FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];

    connection.release();

    if (servers.length === 0) {
      return { success: false, message: '服务器不存在' };
    }
    return { success: true, server: servers[0] };
  } catch (error) {
    console.error('获取服务器信息失败:', error);
    return { success: false, message: '获取服务器信息失败' };
  }
}

// 更新服务器信息
export async function updateServer(serverId: number, userId: number, serverData: any) {
  try {
    const connection = await pool.getConnection();
    const { name, host, port, username, password, sshHost, sshPort, sshUsername, sshPassword, sshPrivateKey } = serverData;

    await connection.execute(
      `UPDATE servers SET
        name = ?, host = ?, port = ?, username = ?, password = ?,
        ssh_host = ?, ssh_port = ?, ssh_username = ?, ssh_password = ?, ssh_private_key = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND user_id = ?`,
      [
        name, host, port, username, password,
        sshHost || null, sshPort || null, sshUsername || null,
        sshPassword || null, sshPrivateKey || null,
        serverId, userId
      ]
    );

    connection.release();
    return { success: true, message: '服务器更新成功' };
  } catch (error) {
    console.error('更新服务器失败:', error);
    return { success: false, message: '更新服务器失败' };
  }
}

// 删除服务器
export async function deleteServer(serverId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    // 检查服务器是否属于该用户
    const [servers] = await connection.execute(
      'SELECT id FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];

    if (servers.length === 0) {
      connection.release();
      return { success: false, message: '服务器不存在或无权限删除' };
    }

    // 删除服务器
    await connection.execute(
      'DELETE FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    );

    connection.release();

    return {
      success: true,
      message: '服务器删除成功'
    };

  } catch (error) {
    console.error('删除服务器失败:', error);
    return { success: false, message: '删除服务器失败，请稍后重试' };
  }
}

// 更新服务器状态
export async function updateServerStatus(serverId: number, status: 'active' | 'inactive' | 'error') {
  try {
    const connection = await pool.getConnection();

    await connection.execute(
      'UPDATE servers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, serverId]
    );

    connection.release();
    return { success: true };
  } catch (error) {
    console.error('更新服务器状态失败:', error);
    return { success: false, message: '更新服务器状态失败' };
  }
}

// 执行数据库备份
export async function executeBackup(taskId: number) {
  try {
    const connection = await pool.getConnection();

    // 获取备份任务详情 - 明确指定字段名以避免冲突
    const [taskRows] = await connection.execute(
      `SELECT
        bt.id as task_id,
        bt.user_id,
        bt.server_id,
        bt.name as task_name,
        bt.database_name,
        bt.backup_type,
        bt.schedule_type,
        bt.schedule_time,
        bt.schedule_day,
        bt.custom_interval_minutes,
        bt.backup_path,
        bt.retention_days,
        bt.status as task_status,
        bt.last_backup_time,
        bt.last_backup_size,
        bt.created_at as task_created_at,
        bt.updated_at as task_updated_at,
        s.id as server_id,
        s.name as server_name,
        s.host,
        s.port,
        s.username,
        s.password,
        s.ssh_host,
        s.ssh_port,
        s.ssh_username,
        s.ssh_password,
        s.ssh_private_key,
        s.status as server_status
       FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.id = ?`,
      [taskId]
    ) as [any[], any];

    if (taskRows.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在' };
    }

    const task = taskRows[0];
    connection.release();

    // 执行备份
    const backupResult = await performMySQLBackup(task);

    if (backupResult.success) {
      // 记录备份历史，使用实际执行的备份类型
      const historyResult = await createBackupHistory({
        taskId: taskId,
        backupType: backupResult.actualBackupType || task.backup_type,
        filePath: backupResult.filePath!,
        fileSize: backupResult.fileSize!,
        startTime: new Date(),
        binlogFile: backupResult.binlogFile,
        binlogPosition: backupResult.binlogPosition
      });

      // 立即更新为完成状态
      if (historyResult.success && historyResult.historyId) {
        await updateBackupHistory(historyResult.historyId, {
          endTime: new Date(),
          status: 'completed'
        });
      }
    }

    return backupResult;
  } catch (error) {
    console.error('执行备份失败:', error);
    return { success: false, message: '执行备份失败' };
  }
}

// 检查用户权限
async function checkUserPrivileges(task: any): Promise<{
  hasReload: boolean;
  hasLockTables: boolean;
  hasShowView: boolean;
}> {
  try {
    const connection = await mysql.createConnection({
      host: task.host,
      port: task.port,
      user: task.username,
      password: task.password,
      database: task.database_name
    });

    // 检查用户权限
    const [grants] = await connection.execute('SHOW GRANTS FOR CURRENT_USER()') as [any[], any];
    const grantText = grants.map((row: any) => Object.values(row)[0]).join(' ').toUpperCase();

    await connection.end();

    return {
      hasReload: grantText.includes('RELOAD') || grantText.includes('ALL PRIVILEGES'),
      hasLockTables: grantText.includes('LOCK TABLES') || grantText.includes('ALL PRIVILEGES'),
      hasShowView: grantText.includes('SHOW VIEW') || grantText.includes('ALL PRIVILEGES')
    };
  } catch (error) {
    console.log('Permission check failed, using conservative mode:', error);
    return {
      hasReload: false,
      hasLockTables: false,
      hasShowView: false
    };
  }
}

// 执行MySQL备份（在服务器端）
async function performMySQLBackup(task: any): Promise<{
  success: boolean;
  message?: string;
  filePath?: string;
  fileSize?: number;
  binlogFile?: string;
  binlogPosition?: number;
  actualBackupType?: 'full' | 'incremental';
}> {
  const { NodeSSH } = await import('node-ssh');
  let ssh: any = null;

  try {
    // 检查是否需要先进行全量备份
    const needsFullBackup = await checkIfNeedsFullBackup(task.task_id);
    let actualBackupType = task.backup_type;

    if (needsFullBackup && task.backup_type === 'incremental') {
      console.log('No full backup found, performing full backup first');
      actualBackupType = 'full';
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${task.database_name}_${actualBackupType}_${timestamp}.sql`;
    const serverFilePath = path.posix.join(task.backup_path, fileName);

    // 建立SSH连接到服务器
    ssh = new (NodeSSH as any)();

    const sshConfig: any = {
      host: task.ssh_host || task.host,
      port: task.ssh_port || 22,
      username: task.ssh_username,
      password: task.ssh_password
    };

    if (task.ssh_private_key) {
      sshConfig.privateKey = task.ssh_private_key;
      delete sshConfig.password;
    }

    console.log('Connecting to server via SSH:', { host: sshConfig.host, port: sshConfig.port, username: sshConfig.username });
    await ssh.connect(sshConfig);
    console.log('SSH connection established successfully');

    // 在服务器上创建备份目录
    await ssh.execCommand(`mkdir -p "${task.backup_path}"`);
    console.log('Backup directory created on server:', task.backup_path);

    // 检查用户权限
    const privileges = await checkUserPrivileges(task);

    // 构建在服务器上执行的mysqldump命令
    const mysqldumpArgs = [
      `--host=${task.host}`,
      `--port=${task.port}`,
      `--user=${task.username}`,
      `--password=${task.password}`,
      '--single-transaction',
      '--lock-tables=false',
      '--no-tablespaces',
      '--skip-add-locks',
      '--skip-disable-keys',
      '--skip-set-charset',
      '--default-character-set=utf8mb4'
    ];

    // 根据实际备份类型添加不同参数
    if (actualBackupType === 'full') {
      // 全量备份：导出完整的数据库结构和数据
      mysqldumpArgs.push('--complete-insert');
      mysqldumpArgs.push('--extended-insert');
      mysqldumpArgs.push('--add-drop-table');
      mysqldumpArgs.push('--hex-blob'); // 处理二进制数据
      mysqldumpArgs.push('--flush-logs'); // 刷新binlog
      mysqldumpArgs.push('--master-data=2'); // 记录binlog位置

      if (privileges.hasShowView) {
        mysqldumpArgs.push('--routines');
        mysqldumpArgs.push('--triggers');
        mysqldumpArgs.push('--events');
      }

      mysqldumpArgs.push(task.database_name);
    } else if (actualBackupType === 'incremental') {
      // 增量备份：基于binlog的真正增量备份
      const lastBinlogInfo = await getLastBinlogInfo(task.task_id);

      if (lastBinlogInfo) {
        // 使用mysqlbinlog进行增量备份
        const binlogBackupResult = await performBinlogBackup(task, lastBinlogInfo, serverFilePath, ssh);

        if (binlogBackupResult.success) {
          return {
            success: true,
            filePath: serverFilePath,
            fileSize: binlogBackupResult.fileSize,
            actualBackupType: 'incremental',
            binlogFile: binlogBackupResult.endBinlogFile,
            binlogPosition: binlogBackupResult.endBinlogPosition
          };
        } else {
          throw new Error(`Binlog备份失败: ${binlogBackupResult.message}`);
        }
      } else {
        // 如果没有找到上次binlog信息，执行全量备份
        console.log('No previous binlog info found, performing full backup');
        actualBackupType = 'full';
        mysqldumpArgs.push('--complete-insert');
        mysqldumpArgs.push('--extended-insert');
        mysqldumpArgs.push('--add-drop-table');
        mysqldumpArgs.push('--hex-blob');
        mysqldumpArgs.push('--flush-logs');
        mysqldumpArgs.push('--master-data=2');

        if (privileges.hasShowView) {
          mysqldumpArgs.push('--routines');
          mysqldumpArgs.push('--triggers');
          mysqldumpArgs.push('--events');
        }

        mysqldumpArgs.push(task.database_name);
      }
    }

    // 在服务器上执行mysqldump命令
    const mysqldumpCommand = `mysqldump ${mysqldumpArgs.join(' ')} > "${serverFilePath}"`;
    console.log('Executing mysqldump command on server:', mysqldumpCommand);

    const result = await ssh.execCommand(mysqldumpCommand);

    if (result.code !== 0) {
      throw new Error(`Mysqldump failed: ${result.stderr}`);
    }

    // 获取备份文件大小
    const sizeResult = await ssh.execCommand(`stat -c%s "${serverFilePath}"`);
    const fileSize = parseInt(sizeResult.stdout.trim()) || 0;

    // 获取当前binlog位置（用于后续增量备份）
    let binlogFile = null;
    let binlogPosition = null;

    if (actualBackupType === 'full') {
      const currentBinlogInfo = await getCurrentBinlogInfo(task, ssh);
      if (currentBinlogInfo.success) {
        binlogFile = currentBinlogInfo.binlogFile;
        binlogPosition = currentBinlogInfo.binlogPosition;
      }
    }

    console.log('Backup completed successfully on server:', {
      filePath: serverFilePath,
      fileSize: fileSize,
      binlogFile: binlogFile,
      binlogPosition: binlogPosition
    });

    return {
      success: true,
      message: '备份成功完成',
      filePath: serverFilePath,
      fileSize: fileSize,
      binlogFile: binlogFile || undefined,
      binlogPosition: binlogPosition || undefined,
      actualBackupType: actualBackupType
    };

  } catch (error: any) {
    console.error('Server backup failed:', error);
    return {
      success: false,
      message: `服务器备份失败: ${error.message}`
    };
  } finally {
    if (ssh) {
      try {
        ssh.dispose();
      } catch (e) {
        console.error('Error closing SSH connection:', e);
      }
    }
  }
}



// 获取服务器端备份文件列表
export async function getServerBackupFiles(serverId: number, userId: number) {
  const { NodeSSH } = await import('node-ssh');
  let ssh: any = null;

  try {
    const connection = await pool.getConnection();

    // 获取服务器信息
    const [servers] = await connection.execute(
      'SELECT * FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];

    connection.release();

    if (servers.length === 0) {
      return { success: false, message: '服务器不存在或无权限' };
    }

    const server = servers[0];

    // 获取该服务器的备份任务路径
    const taskConnection = await pool.getConnection();
    const [tasks] = await taskConnection.execute(
      'SELECT DISTINCT backup_path FROM backup_tasks WHERE server_id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];
    taskConnection.release();

    if (tasks.length === 0) {
      return { success: true, files: [], message: '该服务器暂无备份任务' };
    }

    // 建立SSH连接
    ssh = new (NodeSSH as any)();

    const sshConfig: any = {
      host: server.ssh_host || server.host,
      port: server.ssh_port || 22,
      username: server.ssh_username,
      password: server.ssh_password
    };

    if (server.ssh_private_key) {
      sshConfig.privateKey = server.ssh_private_key;
      delete sshConfig.password;
    }

    await ssh.connect(sshConfig);

    const allFiles: any[] = [];

    // 遍历所有备份路径
    for (const task of tasks) {
      const backupPath = task.backup_path;

      try {
        // 列出备份目录中的文件
        const result = await ssh.execCommand(`find "${backupPath}" -name "*.sql" -type f -exec stat -c "%n|%s|%Y" {} \\;`);

        if (result.code === 0 && result.stdout.trim()) {
          const lines = result.stdout.trim().split('\n');

          for (const line of lines) {
            const [filePath, size, mtime] = line.split('|');
            if (filePath && size && mtime) {
              allFiles.push({
                path: filePath,
                name: path.basename(filePath),
                size: parseInt(size),
                modifiedTime: new Date(parseInt(mtime) * 1000),
                backupPath: backupPath
              });
            }
          }
        }
      } catch (error) {
        console.error(`Error listing files in ${backupPath}:`, error);
      }
    }

    // 按修改时间排序（最新的在前）
    allFiles.sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime());

    return {
      success: true,
      files: allFiles,
      message: `找到 ${allFiles.length} 个备份文件`
    };

  } catch (error: any) {
    console.error('获取服务器备份文件失败:', error);
    return {
      success: false,
      message: `获取服务器备份文件失败: ${error.message}`
    };
  } finally {
    if (ssh) {
      try {
        ssh.dispose();
      } catch (e) {
        console.error('Error closing SSH connection:', e);
      }
    }
  }
}

// 下载服务器端备份文件
export async function downloadServerBackupFile(serverId: number, filePath: string, userId: number) {
  const { NodeSSH } = await import('node-ssh');
  let ssh: any = null;

  try {
    const connection = await pool.getConnection();

    // 获取服务器信息
    const [servers] = await connection.execute(
      'SELECT * FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];

    connection.release();

    if (servers.length === 0) {
      return { success: false, message: '服务器不存在或无权限' };
    }

    const server = servers[0];

    // 建立SSH连接
    ssh = new (NodeSSH as any)();

    const sshConfig: any = {
      host: server.ssh_host || server.host,
      port: server.ssh_port || 22,
      username: server.ssh_username,
      password: server.ssh_password
    };

    if (server.ssh_private_key) {
      sshConfig.privateKey = server.ssh_private_key;
      delete sshConfig.password;
    }

    await ssh.connect(sshConfig);

    // 读取文件内容
    const result = await ssh.execCommand(`cat "${filePath}"`);

    if (result.code !== 0) {
      throw new Error(`读取文件失败: ${result.stderr}`);
    }

    return {
      success: true,
      content: result.stdout,
      fileName: path.basename(filePath)
    };

  } catch (error: any) {
    console.error('下载服务器备份文件失败:', error);
    return {
      success: false,
      message: `下载备份文件失败: ${error.message}`
    };
  } finally {
    if (ssh) {
      try {
        ssh.dispose();
      } catch (e) {
        console.error('Error closing SSH connection:', e);
      }
    }
  }
}

// 检查数据库是否真实存在
export async function checkDatabaseExists(server: any, databaseName: string) {
  let connection: any = null;

  try {
    // 创建到MySQL服务器的连接（不指定数据库）
    const mysqlConfig: any = {
      host: server.host,
      port: server.port,
      user: server.username,
      password: server.password,
      connectTimeout: 10000
    };

    connection = await mysql.createConnection(mysqlConfig);

    // 查询数据库是否存在
    const [rows] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [databaseName]
    ) as [any[], any];

    if (rows.length === 0) {
      return {
        success: false,
        message: `数据库 '${databaseName}' 在服务器上不存在`
      };
    }

    // 尝试连接到具体数据库以验证权限
    await connection.end();

    const dbConfig = {
      ...mysqlConfig,
      database: databaseName
    };

    connection = await mysql.createConnection(dbConfig);

    // 测试基本查询权限
    await connection.execute('SELECT 1');

    return {
      success: true,
      message: '数据库验证成功'
    };

  } catch (error: any) {
    console.error('数据库验证失败:', error);

    if (error.code === 'ER_DBACCESS_DENIED_ERROR') {
      return {
        success: false,
        message: `没有访问数据库 '${databaseName}' 的权限`
      };
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      return {
        success: false,
        message: `数据库 '${databaseName}' 不存在`
      };
    } else if (error.code === 'ECONNREFUSED') {
      return {
        success: false,
        message: '无法连接到MySQL服务器'
      };
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      return {
        success: false,
        message: 'MySQL用户名或密码错误'
      };
    } else {
      return {
        success: false,
        message: `数据库连接失败: ${error.message}`
      };
    }
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (e) {
        // 忽略关闭连接时的错误
      }
    }
  }
}

// 通过服务器ID测试数据库连接
export async function testDatabaseConnectionById(serverId: number, databaseName: string, userId: number) {
  try {
    const connection = await pool.getConnection();

    // 获取服务器信息
    const [servers] = await connection.execute(
      'SELECT * FROM servers WHERE id = ? AND user_id = ?',
      [serverId, userId]
    ) as [any[], any];

    connection.release();

    if (servers.length === 0) {
      return {
        success: false,
        message: '服务器不存在或无权限访问'
      };
    }

    const server = servers[0];

    // 使用checkDatabaseExists函数验证数据库
    return await checkDatabaseExists(server, databaseName);

  } catch (error: any) {
    console.error('测试数据库连接失败:', error);
    return {
      success: false,
      message: `测试数据库连接失败: ${error.message}`
    };
  }
}

// 创建备份任务
export async function createBackupTask(taskData: {
  userId: number;
  serverId: number;
  name: string;
  databaseName: string;
  backupType: 'full' | 'incremental';
  scheduleType: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  scheduleTime?: string;
  scheduleDay?: number;
  customIntervalMinutes?: number;
  backupPath: string;
  retentionDays: number;
}) {
  try {
    const connection = await pool.getConnection();

    // 检查任务名称是否已存在
    const [existing] = await connection.execute(
      'SELECT id FROM backup_tasks WHERE user_id = ? AND name = ?',
      [taskData.userId, taskData.name]
    ) as [any[], any];

    if (existing.length > 0) {
      connection.release();
      return { success: false, message: '备份任务名称已存在' };
    }

    // 验证服务器是否属于该用户并获取服务器信息
    const [servers] = await connection.execute(
      'SELECT * FROM servers WHERE id = ? AND user_id = ?',
      [taskData.serverId, taskData.userId]
    ) as [any[], any];

    if (servers.length === 0) {
      connection.release();
      return { success: false, message: '服务器不存在或无权限' };
    }

    const server = servers[0];

    // 验证数据库是否真实存在
    const databaseExists = await checkDatabaseExists(server, taskData.databaseName);
    if (!databaseExists.success) {
      connection.release();
      return {
        success: false,
        message: `数据库验证失败: ${databaseExists.message}`
      };
    }

    // 插入新备份任务
    const [result] = await connection.execute(
      `INSERT INTO backup_tasks (user_id, server_id, name, database_name, backup_type, schedule_type, schedule_time, schedule_day, custom_interval_minutes, backup_path, retention_days)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskData.userId,
        taskData.serverId,
        taskData.name,
        taskData.databaseName,
        taskData.backupType,
        taskData.scheduleType,
        taskData.scheduleTime || null,
        taskData.scheduleDay || null,
        taskData.customIntervalMinutes || null,
        taskData.backupPath,
        taskData.retentionDays
      ]
    ) as [any, any];

    connection.release();

    return {
      success: true,
      message: '备份任务创建成功',
      taskId: (result as any).insertId
    };

  } catch (error: any) {
    console.error('创建备份任务失败:', error);

    // 提供详细的错误信息
    let errorMessage = '创建备份任务失败';

    if (error.code === 'ER_DUP_ENTRY') {
      errorMessage = '备份任务名称已存在，请使用其他名称';
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      errorMessage = '数据库表不存在，请检查数据库配置';
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      errorMessage = '数据库访问权限不足，请检查用户权限';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '无法连接到数据库服务器，请检查服务器状态';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '数据库连接超时，请检查网络连接';
    } else if (error.message) {
      errorMessage = `创建备份任务失败: ${error.message}`;
    } else {
      errorMessage = `创建备份任务失败: ${error.toString()}`;
    }

    return {
      success: false,
      message: errorMessage,
      errorCode: error.code,
      errorDetails: error.message
    };
  }
}

// 获取用户的备份任务列表
export async function getUserBackupTasks(userId: number) {
  try {
    const connection = await pool.getConnection();

    const [tasks] = await connection.execute(
      `SELECT bt.*, s.name as server_name, s.host as server_host
       FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.user_id = ?
       ORDER BY bt.created_at DESC`,
      [userId]
    ) as [any[], any];

    connection.release();

    return {
      success: true,
      tasks
    };

  } catch (error) {
    console.error('获取备份任务列表失败:', error);
    return { success: false, message: '获取备份任务列表失败' };
  }
}

// 删除备份任务
export async function deleteBackupTask(taskId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    // 检查任务是否属于该用户
    const [tasks] = await connection.execute(
      'SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    ) as [any[], any];

    if (tasks.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在或无权限删除' };
    }

    // 删除备份任务
    await connection.execute(
      'DELETE FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    );

    connection.release();

    return {
      success: true,
      message: '备份任务删除成功'
    };

  } catch (error) {
    console.error('删除备份任务失败:', error);
    return { success: false, message: '删除备份任务失败，请稍后重试' };
  }
}

// 创建备份历史记录
export async function createBackupHistory(historyData: {
  taskId: number;
  backupType: 'full' | 'incremental';
  filePath: string;
  fileSize: number;
  startTime: Date;
  binlogFile?: string;
  binlogPosition?: number;
}) {
  try {
    const connection = await pool.getConnection();

    const [result] = await connection.execute(
      `INSERT INTO backup_history (task_id, backup_type, file_path, file_size, start_time, binlog_file, binlog_position)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        historyData.taskId,
        historyData.backupType,
        historyData.filePath,
        historyData.fileSize,
        historyData.startTime,
        historyData.binlogFile || null,
        historyData.binlogPosition || null
      ]
    ) as [any, any];

    connection.release();

    return {
      success: true,
      historyId: (result as any).insertId
    };

  } catch (error) {
    console.error('创建备份历史记录失败:', error);
    return { success: false, message: '创建备份历史记录失败' };
  }
}

// 更新备份历史记录状态
export async function updateBackupHistory(historyId: number, updateData: {
  endTime?: Date;
  status: 'running' | 'completed' | 'failed';
  errorMessage?: string;
  fileSize?: number;
}) {
  try {
    const connection = await pool.getConnection();

    await connection.execute(
      `UPDATE backup_history
       SET end_time = ?, status = ?, error_message = ?, file_size = ?
       WHERE id = ?`,
      [
        updateData.endTime || null,
        updateData.status,
        updateData.errorMessage || null,
        updateData.fileSize || null,
        historyId
      ]
    );

    connection.release();

    return { success: true };

  } catch (error) {
    console.error('更新备份历史记录失败:', error);
    return { success: false, message: '更新备份历史记录失败' };
  }
}

// 获取备份历史记录
export async function getBackupHistory(userId: number, limit: number = 50) {
  try {
    const connection = await pool.getConnection();

    // 确保limit是有效的数字
    const validLimit = Number.isInteger(limit) && limit > 0 ? limit : 50;

    const [history] = await connection.execute(
      `SELECT bh.*, bt.name as task_name, s.name as server_name, s.host as server_host
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.user_id = ?
       ORDER BY bh.start_time DESC
       LIMIT ${validLimit}`,
      [userId]
    ) as [any[], any];

    connection.release();

    return {
      success: true,
      history
    };

  } catch (error) {
    console.error('获取备份历史记录失败:', error);
    return { success: false, message: '获取备份历史记录失败' };
  }
}

// 获取特定备份任务的详细历史记录
export async function getBackupTaskHistory(taskId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    // 首先验证任务是否属于该用户
    const [taskCheck] = await connection.execute(
      'SELECT id, name, database_name FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    ) as [any[], any];

    if (taskCheck.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在或无权限访问' };
    }

    // 获取该任务的所有备份历史记录
    const [history] = await connection.execute(
      `SELECT bh.*, bt.name as task_name, bt.database_name, s.name as server_name, s.host as server_host
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bh.task_id = ?
       ORDER BY bh.start_time DESC`,
      [taskId]
    ) as [any[], any];

    connection.release();

    return {
      success: true,
      task: taskCheck[0],
      history
    };

  } catch (error) {
    console.error('获取备份任务历史记录失败:', error);
    return { success: false, message: '获取备份任务历史记录失败' };
  }
}

// 获取备份文件路径
export async function getBackupFilePath(historyId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    const [rows] = await connection.execute(
      `SELECT bh.file_path, bt.user_id
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       WHERE bh.id = ? AND bt.user_id = ?`,
      [historyId, userId]
    ) as [any[], any];

    connection.release();

    if (rows.length === 0) {
      return { success: false, message: '备份文件不存在或无权限访问' };
    }

    const filePath = rows[0].file_path;

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return { success: false, message: '备份文件已被删除或移动' };
    }

    return {
      success: true,
      filePath
    };
  } catch (error) {
    console.error('获取备份文件路径失败:', error);
    return { success: false, message: '获取备份文件路径失败' };
  }
}

// 获取需要执行的定时任务
export async function getScheduledTasks() {
  try {
    const connection = await pool.getConnection();

    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM格式
    const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
    const currentDate = now.getDate(); // 1-31

    // 获取所有活跃的定时任务
    const [allTasks] = await connection.execute(
      `SELECT bt.*, s.* FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.status = 'active'
       AND bt.schedule_type != 'manual'`,
      []
    ) as [any[], any];

    // 过滤出需要执行的任务
    const tasksToExecute = [];

    for (const task of allTasks) {
      let shouldExecute = false;

      switch (task.schedule_type) {
        case 'hourly':
          shouldExecute = true; // 每小时检查一次
          break;
        case 'daily':
          shouldExecute = task.schedule_time === currentTime;
          break;
        case 'weekly':
          shouldExecute = task.schedule_time === currentTime && task.schedule_day === currentDay;
          break;
        case 'monthly':
          shouldExecute = task.schedule_time === currentTime && task.schedule_day === currentDate;
          break;
        case 'custom':
          // 自定义间隔逻辑
          if (task.custom_interval_minutes && task.last_backup_time) {
            const lastBackupTime = new Date(task.last_backup_time);
            const minutesSinceLastBackup = (now.getTime() - lastBackupTime.getTime()) / (1000 * 60);
            shouldExecute = minutesSinceLastBackup >= task.custom_interval_minutes;
          } else if (task.custom_interval_minutes && !task.last_backup_time) {
            // 如果从未备份过，则立即执行
            shouldExecute = true;
          }
          break;
      }

      if (shouldExecute) {
        tasksToExecute.push(task);
      }
    }

    const tasks = tasksToExecute;

    connection.release();

    return {
      success: true,
      tasks
    };
  } catch (error) {
    console.error('获取定时任务失败:', error);
    return { success: false, message: '获取定时任务失败', tasks: [] };
  }
}

// 测试服务器连接（包括SSH隧道和MySQL连接）
export async function testServerConnection(serverConfig: {
  host: string;
  port: number;
  username: string;
  password: string;
  sshHost?: string;
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string;
  sshPrivateKey?: string;
}) {
  let ssh: NodeSSH | null = null;
  let connection: mysql.Connection | null = null;

  try {
    console.log('Starting server connection test:', {
      host: serverConfig.host,
      port: serverConfig.port,
      hasSSH: !!serverConfig.sshHost
    });

    // 如果配置了SSH，先测试SSH连接
    if (serverConfig.sshHost && serverConfig.sshUsername) {
      console.log('Testing SSH connection...');
      ssh = new NodeSSH();

      const sshConfig = {
        host: serverConfig.sshHost,
        port: serverConfig.sshPort || 22,
        username: serverConfig.sshUsername,
      } as any;

      // 优先使用私钥，否则使用密码
      if (serverConfig.sshPrivateKey) {
        sshConfig.privateKey = serverConfig.sshPrivateKey;
      } else if (serverConfig.sshPassword) {
        sshConfig.password = serverConfig.sshPassword;
      } else {
        return {
          success: false,
          message: 'SSH连接需要提供密码或私钥',
          details: { step: 'ssh_auth_validation' }
        };
      }

      try {
        await ssh.connect(sshConfig);
        console.log('SSH connection successful');
      } catch (sshError) {
        console.error('SSH连接失败:', sshError);
        return {
          success: false,
          message: `SSH连接失败: ${sshError instanceof Error ? sshError.message : String(sshError)}`,
          details: {
            step: 'ssh_connection',
            sshHost: serverConfig.sshHost,
            sshPort: serverConfig.sshPort,
            sshUsername: serverConfig.sshUsername,
            errorMessage: sshError instanceof Error ? sshError.message : String(sshError)
          }
        };
      }
    }

    // 测试MySQL连接
    console.log('Testing MySQL connection...');
    const mysqlConfig: mysql.ConnectionOptions = {
      host: serverConfig.host,
      port: serverConfig.port,
      user: serverConfig.username,
      password: serverConfig.password,
      connectTimeout: 10000
    };

    try {
      connection = await mysql.createConnection(mysqlConfig);
      console.log('MySQL connection successful');

      // 获取MySQL版本信息
      const [rows] = await connection.execute('SELECT VERSION() as version') as [any[], any];
      const version = rows[0]?.version;

      // 测试基本权限
      await connection.execute('SHOW DATABASES');

      return {
        success: true,
        message: 'MySQL连接测试成功',
        details: {
          version,
          host: serverConfig.host,
          port: serverConfig.port,
          username: serverConfig.username,
          sshEnabled: !!serverConfig.sshHost
        }
      };

    } catch (mysqlError) {
      console.error('MySQL连接失败:', mysqlError);
      let errorMessage = 'MySQL连接失败';

      if (mysqlError instanceof Error) {
        if (mysqlError.message.includes('ECONNREFUSED')) {
          errorMessage = `无法连接到MySQL服务器 ${serverConfig.host}:${serverConfig.port}，请检查服务器地址和端口是否正确`;
        } else if (mysqlError.message.includes('Access denied')) {
          errorMessage = `MySQL认证失败，请检查用户名和密码是否正确`;
        } else if (mysqlError.message.includes('ETIMEDOUT')) {
          errorMessage = `连接MySQL服务器超时，请检查网络连接和防火墙设置`;
        } else {
          errorMessage = `MySQL连接失败: ${mysqlError.message}`;
        }
      }

      return {
        success: false,
        message: errorMessage,
        details: {
          step: 'mysql_connection',
          host: serverConfig.host,
          port: serverConfig.port,
          username: serverConfig.username,
          errorMessage: mysqlError instanceof Error ? mysqlError.message : String(mysqlError)
        }
      };
    }

  } catch (error) {
    console.error('连接测试过程中发生未知错误:', error);
    return {
      success: false,
      message: `连接测试失败: ${error instanceof Error ? error.message : String(error)}`,
      details: {
        step: 'unknown_error',
        errorMessage: error instanceof Error ? error.message : String(error)
      }
    };
  } finally {
    // 清理连接
    if (connection) {
      try {
        await connection.end();
      } catch (e) {
        console.error('关闭MySQL连接时出错:', e);
      }
    }
    if (ssh) {
      try {
        ssh.dispose();
      } catch (e) {
        console.error('关闭SSH连接时出错:', e);
      }
    }
  }
}

// 测试数据库是否存在
export async function testDatabaseExists(serverConfig: {
  host: string;
  port: number;
  username: string;
  password: string;
  sshHost?: string;
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string;
  sshPrivateKey?: string;
}, databaseName: string) {
  let connection: mysql.Connection | null = null;

  try {
    console.log('Testing database existence:', { database: databaseName });

    const mysqlConfig: mysql.ConnectionOptions = {
      host: serverConfig.host,
      port: serverConfig.port,
      user: serverConfig.username,
      password: serverConfig.password,
      connectTimeout: 10000
    };

    connection = await mysql.createConnection(mysqlConfig);

    // 查询数据库是否存在
    const [rows] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [databaseName]
    ) as [any[], any];

    const exists = rows.length > 0;

    return {
      success: true,
      exists,
      message: exists ? `数据库 ${databaseName} 存在` : `数据库 ${databaseName} 不存在`
    };

  } catch (error) {
    console.error('测试数据库存在性失败:', error);
    return {
      success: false,
      message: `测试数据库失败: ${error instanceof Error ? error.message : String(error)}`,
      details: {
        errorMessage: error instanceof Error ? error.message : String(error)
      }
    };
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (e) {
        console.error('关闭MySQL连接时出错:', e);
      }
    }
  }
}

// 关闭数据库连接
export async function closeDatabase() {
  if (pool) {
    await pool.end();
  }
}

// 生成完整的SQL文件（从全量备份到指定时间点）
export async function generateCompleteBackupSQL(taskId: number, targetHistoryId: number, userId: number) {
  try {
    const connection = await pool.getConnection();

    // 验证任务权限
    const [taskCheck] = await connection.execute(
      'SELECT id, name, database_name, backup_path FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    ) as [any[], any];

    if (taskCheck.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在或无权限访问' };
    }

    const task = taskCheck[0];

    // 获取目标备份记录
    const [targetBackup] = await connection.execute(
      'SELECT * FROM backup_history WHERE id = ? AND task_id = ?',
      [targetHistoryId, taskId]
    ) as [any[], any];

    if (targetBackup.length === 0) {
      connection.release();
      return { success: false, message: '目标备份记录不存在' };
    }

    const target = targetBackup[0];

    // 获取该任务的所有备份记录，按时间排序
    const [allBackups] = await connection.execute(
      `SELECT * FROM backup_history
       WHERE task_id = ? AND start_time <= ? AND status = 'completed'
       ORDER BY start_time ASC`,
      [taskId, target.start_time]
    ) as [any[], any];

    connection.release();

    if (allBackups.length === 0) {
      return { success: false, message: '没有找到可用的备份记录' };
    }

    // 找到最近的全量备份
    let fullBackup = null;
    let incrementalBackups = [];

    for (let i = allBackups.length - 1; i >= 0; i--) {
      if (allBackups[i].backup_type === 'full') {
        fullBackup = allBackups[i];
        incrementalBackups = allBackups.slice(i + 1);
        break;
      }
    }

    if (!fullBackup) {
      return { success: false, message: '没有找到全量备份，无法生成完整SQL文件' };
    }

    // 生成合并的SQL文件
    const outputDir = path.join(task.backup_path, 'merged');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFileName = `${task.database_name}_complete_${timestamp}.sql`;
    const outputPath = path.join(outputDir, outputFileName);

    // 创建输出文件
    const outputStream = fs.createWriteStream(outputPath);

    // 写入文件头注释
    outputStream.write(`-- MySQL 完整备份文件\n`);
    outputStream.write(`-- 数据库: ${task.database_name}\n`);
    outputStream.write(`-- 生成时间: ${new Date().toISOString()}\n`);
    outputStream.write(`-- 基于全量备份: ${fullBackup.file_path}\n`);
    outputStream.write(`-- 包含增量备份数量: ${incrementalBackups.length}\n`);
    outputStream.write(`-- 恢复到时间点: ${target.start_time}\n`);
    outputStream.write(`\n-- ========================================\n\n`);

    // 获取服务器连接信息
    const serverInfo = await getServerInfoByTaskId(taskId);
    if (!serverInfo.success) {
      outputStream.end();
      return { success: false, message: `获取服务器信息失败: ${serverInfo.message}` };
    }

    // 建立SSH连接下载备份文件
    const { NodeSSH } = await import('node-ssh');
    const ssh = new (NodeSSH as any)();

    try {
      await ssh.connect({
        host: serverInfo.server.ssh_host || serverInfo.server.host,
        port: serverInfo.server.ssh_port || 22,
        username: serverInfo.server.ssh_username,
        password: serverInfo.server.ssh_password
      });

      // 写入全量备份内容
      const fullBackupContent = await downloadBackupFileContent(ssh, fullBackup.file_path);
      if (!fullBackupContent.success) {
        outputStream.end();
        return { success: false, message: `下载全量备份文件失败: ${fullBackupContent.message}` };
      }

      outputStream.write(`-- 全量备份内容 (${fullBackup.start_time})\n`);
      outputStream.write(fullBackupContent.content);
      outputStream.write('\n\n');

      // 按顺序写入增量备份内容
      for (const incrementalBackup of incrementalBackups) {
        const incrementalContent = await downloadBackupFileContent(ssh, incrementalBackup.file_path);
        if (incrementalContent.success) {
          outputStream.write(`-- 增量备份内容 (${incrementalBackup.start_time})\n`);
          outputStream.write(incrementalContent.content);
          outputStream.write('\n\n');
        } else {
          console.warn(`下载增量备份文件失败: ${incrementalBackup.file_path}, 错误: ${incrementalContent.message}`);
          outputStream.write(`-- 警告: 增量备份文件下载失败 (${incrementalBackup.start_time}): ${incrementalContent.message}\n\n`);
        }
      }

    } finally {
      if (ssh) {
        ssh.dispose();
      }
    }

    outputStream.write(`-- 备份合并完成\n`);
    outputStream.end();

    // 获取文件大小
    const stats = fs.statSync(outputPath);

    return {
      success: true,
      message: '完整SQL文件生成成功',
      filePath: outputPath,
      fileName: outputFileName,
      fileSize: stats.size,
      fullBackup: {
        id: fullBackup.id,
        start_time: fullBackup.start_time,
        file_size: fullBackup.file_size
      },
      incrementalBackups: incrementalBackups.map(backup => ({
        id: backup.id,
        start_time: backup.start_time,
        file_size: backup.file_size
      }))
    };

  } catch (error) {
    console.error('生成完整SQL文件失败:', error);
    return { success: false, message: '生成完整SQL文件失败' };
  }
}

// 检查是否需要全量备份
async function checkIfNeedsFullBackup(taskId: number): Promise<boolean> {
  try {
    const connection = await pool.getConnection();

    // 查找该任务的最近一次全量备份
    const [fullBackups] = await connection.execute(
      `SELECT id FROM backup_history
       WHERE task_id = ? AND backup_type = 'full' AND status = 'completed'
       ORDER BY start_time DESC
       LIMIT 1`,
      [taskId]
    ) as [any[], any];

    connection.release();

    // 如果没有找到全量备份，则需要进行全量备份
    return fullBackups.length === 0;
  } catch (error) {
    console.error('检查全量备份失败:', error);
    // 出错时默认需要全量备份
    return true;
  }
}

// 获取最后一次备份的binlog信息
async function getLastBinlogInfo(taskId: number): Promise<{binlogFile: string, binlogPosition: number} | null> {
  try {
    const connection = await pool.getConnection();

    // 获取最近一次成功备份的binlog信息
    const [backups] = await connection.execute(
      `SELECT binlog_file, binlog_position FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       AND binlog_file IS NOT NULL AND binlog_position IS NOT NULL
       ORDER BY start_time DESC
       LIMIT 1`,
      [taskId]
    ) as [any[], any];

    connection.release();

    if (backups.length > 0) {
      return {
        binlogFile: backups[0].binlog_file,
        binlogPosition: backups[0].binlog_position
      };
    }

    return null;
  } catch (error) {
    console.error('获取最后binlog信息失败:', error);
    return null;
  }
}

// 获取最后一次备份的时间（保留用于兼容性）
async function getLastBackupTime(taskId: number): Promise<Date | null> {
  try {
    const connection = await pool.getConnection();

    // 获取最近一次成功备份的时间
    const [backups] = await connection.execute(
      `SELECT start_time FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time DESC
       LIMIT 1`,
      [taskId]
    ) as [any[], any];

    connection.release();

    if (backups.length > 0) {
      return new Date(backups[0].start_time);
    }

    return null;
  } catch (error) {
    console.error('获取最后备份时间失败:', error);
    return null;
  }
}

// 执行基于binlog的增量备份
async function performBinlogBackup(
  task: any,
  lastBinlogInfo: {binlogFile: string, binlogPosition: number},
  outputFilePath: string,
  ssh: any
): Promise<{success: boolean, fileSize?: number, endBinlogFile?: string, endBinlogPosition?: number, message?: string}> {
  try {
    // 1. 获取当前binlog位置
    const currentBinlogInfo = await getCurrentBinlogInfo(task, ssh);
    if (!currentBinlogInfo.success) {
      return { success: false, message: `获取当前binlog位置失败: ${currentBinlogInfo.message}` };
    }

    // 2. 检查是否有新的binlog数据
    if (lastBinlogInfo.binlogFile === currentBinlogInfo.binlogFile &&
        lastBinlogInfo.binlogPosition >= currentBinlogInfo.binlogPosition) {
      // 没有新数据，创建空的增量备份文件
      const emptyBackupContent = `-- MySQL增量备份文件\n-- 数据库: ${task.database_name}\n-- 时间范围: ${lastBinlogInfo.binlogFile}:${lastBinlogInfo.binlogPosition} 到 ${currentBinlogInfo.binlogFile}:${currentBinlogInfo.binlogPosition}\n-- 状态: 无新数据变更\n`;

      await ssh.execCommand(`echo "${emptyBackupContent}" > "${outputFilePath}"`);

      return {
        success: true,
        fileSize: emptyBackupContent.length,
        endBinlogFile: currentBinlogInfo.binlogFile,
        endBinlogPosition: currentBinlogInfo.binlogPosition
      };
    }

    // 3. 使用mysqlbinlog提取增量数据
    const mysqlbinlogArgs = [
      `--host=${task.host}`,
      `--port=${task.port}`,
      `--user=${task.username}`,
      `--password=${task.password}`,
      `--database=${task.database_name}`,
      `--start-position=${lastBinlogInfo.binlogPosition}`,
      `--stop-position=${currentBinlogInfo.binlogPosition}`,
      '--base64-output=DECODE-ROWS',
      '--verbose',
      lastBinlogInfo.binlogFile
    ];

    // 如果binlog文件不同，需要处理多个文件
    if (lastBinlogInfo.binlogFile !== currentBinlogInfo.binlogFile) {
      // 获取binlog文件列表
      const binlogFiles = await getBinlogFilesBetween(task, lastBinlogInfo.binlogFile, currentBinlogInfo.binlogFile, ssh);
      if (!binlogFiles.success) {
        return { success: false, message: `获取binlog文件列表失败: ${binlogFiles.message}` };
      }

      // 处理多个binlog文件
      let combinedContent = `-- MySQL增量备份文件\n-- 数据库: ${task.database_name}\n-- 开始位置: ${lastBinlogInfo.binlogFile}:${lastBinlogInfo.binlogPosition}\n-- 结束位置: ${currentBinlogInfo.binlogFile}:${currentBinlogInfo.binlogPosition}\n\n`;

      for (const binlogFile of binlogFiles.files) {
        const startPos = binlogFile === lastBinlogInfo.binlogFile ? lastBinlogInfo.binlogPosition : 4;
        const endPos = binlogFile === currentBinlogInfo.binlogFile ? currentBinlogInfo.binlogPosition : undefined;

        const binlogArgs = [
          `--host=${task.host}`,
          `--port=${task.port}`,
          `--user=${task.username}`,
          `--password=${task.password}`,
          `--database=${task.database_name}`,
          `--start-position=${startPos}`,
          ...(endPos ? [`--stop-position=${endPos}`] : []),
          '--base64-output=DECODE-ROWS',
          '--verbose',
          binlogFile
        ];

        const binlogCommand = `mysqlbinlog ${binlogArgs.join(' ')}`;
        const result = await ssh.execCommand(binlogCommand);

        if (result.code !== 0) {
          return { success: false, message: `mysqlbinlog执行失败: ${result.stderr}` };
        }

        combinedContent += `-- Binlog文件: ${binlogFile}\n${result.stdout}\n\n`;
      }

      // 写入合并后的内容
      await ssh.execCommand(`cat > "${outputFilePath}" << 'EOF'\n${combinedContent}\nEOF`);
    } else {
      // 单个binlog文件处理
      const mysqlbinlogCommand = `mysqlbinlog ${mysqlbinlogArgs.join(' ')} > "${outputFilePath}"`;
      const result = await ssh.execCommand(mysqlbinlogCommand);

      if (result.code !== 0) {
        return { success: false, message: `mysqlbinlog执行失败: ${result.stderr}` };
      }
    }

    // 4. 获取生成文件的大小
    const sizeResult = await ssh.execCommand(`stat -c%s "${outputFilePath}"`);
    const fileSize = parseInt(sizeResult.stdout.trim()) || 0;

    return {
      success: true,
      fileSize: fileSize,
      endBinlogFile: currentBinlogInfo.binlogFile,
      endBinlogPosition: currentBinlogInfo.binlogPosition
    };

  } catch (error) {
    console.error('Binlog备份执行失败:', error);
    return { success: false, message: `Binlog备份执行失败: ${error}` };
  }
}

// 获取当前binlog位置信息
async function getCurrentBinlogInfo(task: any, ssh: any): Promise<{success: boolean, binlogFile?: string, binlogPosition?: number, message?: string}> {
  try {
    // 连接到MySQL并获取当前binlog位置
    const showMasterStatusCommand = `mysql --host=${task.host} --port=${task.port} --user=${task.username} --password=${task.password} -e "SHOW MASTER STATUS\\G"`;

    const result = await ssh.execCommand(showMasterStatusCommand);

    if (result.code !== 0) {
      return { success: false, message: `获取master状态失败: ${result.stderr}` };
    }

    // 解析输出
    const output = result.stdout;
    const fileMatch = output.match(/File:\s*(\S+)/);
    const positionMatch = output.match(/Position:\s*(\d+)/);

    if (!fileMatch || !positionMatch) {
      return { success: false, message: '无法解析binlog位置信息' };
    }

    return {
      success: true,
      binlogFile: fileMatch[1],
      binlogPosition: parseInt(positionMatch[1])
    };

  } catch (error) {
    console.error('获取当前binlog信息失败:', error);
    return { success: false, message: `获取当前binlog信息失败: ${error}` };
  }
}

// 获取两个binlog文件之间的所有文件列表
async function getBinlogFilesBetween(task: any, startFile: string, endFile: string, ssh: any): Promise<{success: boolean, files?: string[], message?: string}> {
  try {
    // 获取所有binlog文件
    const showBinaryLogsCommand = `mysql --host=${task.host} --port=${task.port} --user=${task.username} --password=${task.password} -e "SHOW BINARY LOGS"`;

    const result = await ssh.execCommand(showBinaryLogsCommand);

    if (result.code !== 0) {
      return { success: false, message: `获取binlog文件列表失败: ${result.stderr}` };
    }

    // 解析binlog文件列表
    const lines = result.stdout.split('\n');
    const binlogFiles: string[] = [];

    let startFound = false;
    for (const line of lines) {
      const match = line.match(/(\S+\.?\d+)/);
      if (match) {
        const fileName = match[1];

        if (fileName === startFile) {
          startFound = true;
        }

        if (startFound) {
          binlogFiles.push(fileName);
        }

        if (fileName === endFile) {
          break;
        }
      }
    }

    if (!startFound) {
      return { success: false, message: `未找到起始binlog文件: ${startFile}` };
    }

    return {
      success: true,
      files: binlogFiles
    };

  } catch (error) {
    console.error('获取binlog文件列表失败:', error);
    return { success: false, message: `获取binlog文件列表失败: ${error}` };
  }
}

// 获取任务对应的服务器信息
async function getServerInfoByTaskId(taskId: number): Promise<{success: boolean, server?: any, message?: string}> {
  try {
    const connection = await pool.getConnection();

    const [result] = await connection.execute(
      `SELECT s.* FROM servers s
       JOIN backup_tasks bt ON s.id = bt.server_id
       WHERE bt.id = ?`,
      [taskId]
    ) as [any[], any];

    connection.release();

    if (result.length === 0) {
      return { success: false, message: '未找到对应的服务器信息' };
    }

    return {
      success: true,
      server: result[0]
    };

  } catch (error) {
    console.error('获取服务器信息失败:', error);
    return { success: false, message: `获取服务器信息失败: ${error}` };
  }
}

// 下载备份文件内容
async function downloadBackupFileContent(ssh: any, filePath: string): Promise<{success: boolean, content?: string, message?: string}> {
  try {
    // 检查文件是否存在
    const checkResult = await ssh.execCommand(`test -f "${filePath}" && echo "exists" || echo "not found"`);

    if (checkResult.stdout.trim() !== 'exists') {
      return { success: false, message: '文件不存在' };
    }

    // 读取文件内容
    const result = await ssh.execCommand(`cat "${filePath}"`);

    if (result.code !== 0) {
      return { success: false, message: `读取文件失败: ${result.stderr}` };
    }

    return {
      success: true,
      content: result.stdout
    };

  } catch (error) {
    console.error('下载备份文件内容失败:', error);
    return { success: false, message: `下载备份文件内容失败: ${error}` };
  }
}

// 验证备份链的binlog连续性
export async function validateBackupChainContinuity(taskId: number, userId: number): Promise<{success: boolean, isValid?: boolean, issues?: string[], message?: string}> {
  try {
    const connection = await pool.getConnection();

    // 验证权限
    const [taskCheck] = await connection.execute(
      'SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    ) as [any[], any];

    if (taskCheck.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在或无权限访问' };
    }

    // 获取所有成功的备份记录，按时间排序
    const [backups] = await connection.execute(
      `SELECT id, backup_type, start_time, binlog_file, binlog_position, file_path
       FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time ASC`,
      [taskId]
    ) as [any[], any];

    connection.release();

    if (backups.length === 0) {
      return { success: true, isValid: true, issues: [] };
    }

    const issues: string[] = [];
    let isValid = true;

    // 检查是否有全量备份作为起点
    const hasFullBackup = backups.some((backup: any) => backup.backup_type === 'full');
    if (!hasFullBackup) {
      issues.push('缺少全量备份作为基础');
      isValid = false;
    }

    // 检查binlog连续性
    for (let i = 1; i < backups.length; i++) {
      const prevBackup = backups[i - 1];
      const currentBackup = backups[i];

      // 如果当前备份是增量备份，检查binlog连续性
      if (currentBackup.backup_type === 'incremental') {
        if (!prevBackup.binlog_file || !prevBackup.binlog_position) {
          issues.push(`备份 ${prevBackup.id} 缺少binlog位置信息`);
          isValid = false;
        }

        if (!currentBackup.binlog_file || !currentBackup.binlog_position) {
          issues.push(`备份 ${currentBackup.id} 缺少binlog位置信息`);
          isValid = false;
        }

        // 检查binlog位置是否连续
        if (prevBackup.binlog_file && currentBackup.binlog_file) {
          if (prevBackup.binlog_file === currentBackup.binlog_file) {
            // 同一个binlog文件，位置应该递增
            if (prevBackup.binlog_position >= currentBackup.binlog_position) {
              issues.push(`备份 ${currentBackup.id} 的binlog位置不连续`);
              isValid = false;
            }
          }
          // 不同binlog文件的连续性检查需要更复杂的逻辑，这里简化处理
        }
      }
    }

    return {
      success: true,
      isValid: isValid,
      issues: issues
    };

  } catch (error) {
    console.error('验证备份链连续性失败:', error);
    return { success: false, message: `验证备份链连续性失败: ${error}` };
  }
}

// 验证备份文件完整性
export async function validateBackupFileIntegrity(historyId: number, userId: number): Promise<{success: boolean, isValid?: boolean, fileSize?: number, message?: string}> {
  try {
    const connection = await pool.getConnection();

    // 获取备份记录和任务信息
    const [backupInfo] = await connection.execute(
      `SELECT bh.*, bt.user_id, bt.server_id
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       WHERE bh.id = ? AND bt.user_id = ?`,
      [historyId, userId]
    ) as [any[], any];

    if (backupInfo.length === 0) {
      connection.release();
      return { success: false, message: '备份记录不存在或无权限访问' };
    }

    const backup = backupInfo[0];

    // 获取服务器信息
    const [serverInfo] = await connection.execute(
      'SELECT * FROM servers WHERE id = ?',
      [backup.server_id]
    ) as [any[], any];

    connection.release();

    if (serverInfo.length === 0) {
      return { success: false, message: '服务器信息不存在' };
    }

    const server = serverInfo[0];

    // 建立SSH连接验证文件
    const { NodeSSH } = await import('node-ssh');
    const ssh = new (NodeSSH as any)();

    try {
      await ssh.connect({
        host: server.ssh_host || server.host,
        port: server.ssh_port || 22,
        username: server.ssh_username,
        password: server.ssh_password
      });

      // 检查文件是否存在
      const checkResult = await ssh.execCommand(`test -f "${backup.file_path}" && echo "exists" || echo "not found"`);

      if (checkResult.stdout.trim() !== 'exists') {
        return {
          success: true,
          isValid: false,
          message: '备份文件不存在'
        };
      }

      // 获取文件大小
      const sizeResult = await ssh.execCommand(`stat -c%s "${backup.file_path}"`);
      const actualFileSize = parseInt(sizeResult.stdout.trim()) || 0;

      // 比较文件大小
      const sizeMatches = actualFileSize === backup.file_size;

      // 简单的文件头验证（检查是否是有效的SQL文件）
      const headResult = await ssh.execCommand(`head -n 5 "${backup.file_path}"`);
      const isValidSQL = headResult.stdout.includes('MySQL') ||
                        headResult.stdout.includes('mysqldump') ||
                        headResult.stdout.includes('--') ||
                        headResult.stdout.includes('CREATE') ||
                        headResult.stdout.includes('INSERT');

      const isValid = sizeMatches && isValidSQL;

      return {
        success: true,
        isValid: isValid,
        fileSize: actualFileSize,
        message: isValid ? '文件完整性验证通过' :
                 !sizeMatches ? `文件大小不匹配，期望: ${backup.file_size}, 实际: ${actualFileSize}` :
                 '文件格式验证失败'
      };

    } finally {
      if (ssh) {
        ssh.dispose();
      }
    }

  } catch (error) {
    console.error('验证备份文件完整性失败:', error);
    return { success: false, message: `验证备份文件完整性失败: ${error}` };
  }
}

// 批量验证任务的所有备份文件
export async function validateAllBackupFiles(taskId: number, userId: number): Promise<{success: boolean, results?: any[], message?: string}> {
  try {
    const connection = await pool.getConnection();

    // 验证权限
    const [taskCheck] = await connection.execute(
      'SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?',
      [taskId, userId]
    ) as [any[], any];

    if (taskCheck.length === 0) {
      connection.release();
      return { success: false, message: '备份任务不存在或无权限访问' };
    }

    // 获取所有成功的备份记录
    const [backups] = await connection.execute(
      `SELECT id, backup_type, start_time, file_path, file_size
       FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time DESC`,
      [taskId]
    ) as [any[], any];

    connection.release();

    const results = [];

    // 验证每个备份文件
    for (const backup of backups) {
      const validationResult = await validateBackupFileIntegrity(backup.id, userId);
      results.push({
        historyId: backup.id,
        backupType: backup.backup_type,
        startTime: backup.start_time,
        filePath: backup.file_path,
        expectedSize: backup.file_size,
        validation: validationResult
      });
    }

    return {
      success: true,
      results: results
    };

  } catch (error) {
    console.error('批量验证备份文件失败:', error);
    return { success: false, message: `批量验证备份文件失败: ${error}` };
  }
}
